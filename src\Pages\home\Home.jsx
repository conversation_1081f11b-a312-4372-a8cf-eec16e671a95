import React, { useState, useEffect } from "react";
import { Card, Avatar } from "rsuite";
import { useNavigate } from "react-router-dom";
import { fetchAssistantData } from "../../Services/api";
import { DUMMY_NEWS_DATA } from "../constants";
import ChatWidget from "../../components/ChatWidget";
import TicketDisplay from "../../components/TicketDisplay";

const Home = () => {
  const [assistantData, setAssistantData] = useState([]); // Stores fetched assistants
  const navigate = useNavigate(); // Handles navigation

  // Sample ticket data - you can replace this with actual data from your API or state
  const sampleTicketData = {
    bookingReference: "PNR002",
    passengerName: "Amit kumar PNR",
    flightNumber: "VA123",
    from: "London",
    to: "New York",
    travelDate: "2025-04-10",
    ticketAmount: "N/A"
  };

  // Handle verify ticket action
  const handleVerifyTicket = (ticketData) => {
    console.log("Verifying ticket:", ticketData);
    // Add your verification logic here
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        const assistants = await fetchAssistantData(); // Fetch data
        setAssistantData(assistants);
      } catch (error) {
        console.error("❌ Fetching Assistant Data Error:", error);
      }
    };

    fetchData();
  }, []); // Runs once on mount

  return (
    <>
      {/* Latest Apps Section */}
      <hr style={{ border: "1px solid #e5e7eb" }} />
      <section className="latest-section">
        <h2 className="section-title">Latest Apps Used</h2>
        <div className="card-container">
          {assistantData.length > 0 ? (
            assistantData.map((assistant) => (
              <Card
                key={assistant.id}
                className="assistant-card"
                onClick={() =>
                  navigate("/chat", {
                    state: { 
                      assistant, 
                      availableAssistants: assistantData 
                    }
                  })
                }
              >
                <div className="card-header">
                  <div style={{ width: "15%" }}>
                    <Avatar
                      circle
                      src="../../assets/images/Virgin-atlantic-logo.jpg"
                      className="assistant-avatar"
                      alt="Assistant Avatar"
                    />
                  </div>
                  <div className="assistant-info">
                    <h6 className="assistant-title">{assistant.name}</h6>
                    <p className="assistant-message">
                      {assistant.getShortDescription() || "No description available."}
                    </p>
                  </div>
                </div>
              </Card>
            ))
          ) : (
            <p>No assistants available.</p>
          )}
        </div>
      </section>

      {/* Ticket Display Section */}
      <hr style={{ border: "1px solid #e5e7eb", marginTop: "30px" }} />
      <section className="latest-section">
        <h2 className="section-title">Recent Ticket</h2>
        <div className="ticket-section" style={{ 
          display: 'flex', 
          justifyContent: 'center',
          margin: '20px 0'
        }}>
          <TicketDisplay 
            ticketData={sampleTicketData}
            onVerify={handleVerifyTicket}
          />
        </div>
      </section>

      {/* Latest News Section */}
      <hr style={{ border: "1px solid #e5e7eb", marginTop: "30px" }} />
      <section className="latest-section">
        <h2 className="section-title">Latest News</h2>
        <div className="card-container">
          {DUMMY_NEWS_DATA.length > 0 ? (
            DUMMY_NEWS_DATA.map((article, index) => (
              <Card key={index} className="news-card">
                <div className="news-header">
                  <div style={{ width: "15%" }}>
                    <Avatar
                      circle
                      src="https://placehold.co/80x80"
                      className="news-avatar"
                      alt="News Avatar"
                    />
                  </div>
                  <div className="news-info">
                    <h4 className="news-title">
                      <a
                        href={article.link}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        {article.title}
                      </a>
                    </h4>
                    <p className="news-snippet">
                      {article.contentSnippet || "No content available."}
                    </p>
                    <p className="muted-text">
                      Published: {new Date(article.pubDate).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              </Card>
            ))
          ) : (
            <p>No news available.</p>
          )}
        </div>
      </section>
      <ChatWidget />
    </>
  );
};

export default Home;


