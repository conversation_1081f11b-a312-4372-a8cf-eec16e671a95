@import url("https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&display=swap");
@import "rsuite/dist/rsuite.min.css";

/* =========================
   COLOR VARIABLES
========================= */
/* :root {
  --sidebar-width: 300px;
  --sidebar-bg: #1d1f23;
  --main-bg: #f3f4f6;
  --header-bg: #0f172a;
  --card-bg: #ffffff;
  --text-color: #111827;
  --body-text-color: #374151;
  --muted-text: #6b7280;
  --hover-bg: #e5e7eb;
  --link-color: #4f94ff;
  --accent-color: #2563eb;
  --search-bg: #ffffff;
  --search-font: "Inter", sans-serif;
} */
:root {
  --sidebar-width: 300px;
  --sidebar-bg: #ffffff;           /* Sidebar now uses a white background */
  --main-bg: #f3f4f6;              /* Main background remains light gray */
  --header-bg: #d71921;            /* Header uses Virgin Atlantic red */
  --card-bg: #ffffff;
  --text-color: #111827;           /* Dark text for legibility */
  --body-text-color: #374151;
  --muted-text: #6b7280;
  --hover-bg: #e5e7eb;
  --link-color: #d71921;           /* Virgin Atlantic red for links */
  --accent-color: #d71921;         /* Virgin Atlantic red for accents */
  --search-bg: #ffffff;
  --search-font: "Helvetica Neue", Helvetica, Arial, sans-serif; /* Virgin-inspired font */
  --link-color: #d71921;
  --accent-color: #d71921;
}

/* =========================
   GLOBAL STYLES
========================= */
body {
  /* font-family: Apple-System, Arial, Helvetica, "PingFang SC", "Hiragino Sans GB",
    "Microsoft YaHei", STXihei, sans-serif; */
    font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  margin: 0;
  padding: 0;
  background-color: var(--main-bg);
  color: var(--body-text-color);
  overflow-y: auto !important;
}

/* =========================
   APP CONTAINER (MAIN LAYOUT)
========================= */
.app-container {
  display: flex;
  height: 100vh;
  overflow: hidden;
}

/* =========================
   SIDEBAR
========================= */
.sidebar {
  display: flex;
  flex-direction: column;
  background: var(--sidebar-bg); /* White background */
  color: var(--text-color);       /* Use dark text on white */
  padding: 20px;
  overflow: hidden;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.2);
}

/* Sidebar Header */
.brand {
  padding: 10px 18px;
  font-size: 16px;
  font-weight: bold;
  text-transform: uppercase;
  white-space: nowrap;
  overflow: hidden;
}

.brand a {
  text-decoration: none;
  color: var(--accent-color);  /* Virgin red for brand text */
}


/* Sidebar Profile Section */
.sidebar-profile {
  display: flex;
  align-items: center;
  gap: 10px;
  padding-bottom: 21px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1); /* subtle divider */
}

.profile-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-grow: 1;
  justify-content: space-between;
}

.sidebar-profile-name {
  font-size: 14px;
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: var(--text-color);
}

.settings-btn {
  background: transparent !important;
  border: none !important;
  cursor: pointer;
  color: var(--accent-color) !important;
  margin-right: 20px;
}

.settings-btn:hover {
  color: var(--link-color) !important;
}

/* Navigation Links */
.sidebar-nav {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  margin-top: 20px;
}

.nav-item {
  padding: 12px;
  color: rgba(0, 0, 0, 0.8);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 15px;
  border-radius: 5px;
  transition: background 0.2s ease-in-out;
}

.nav-item:hover {
  background-color: var(--hover-bg);
  color: var(--text-color);
}

.nav-item.active {
  background-color: var(--accent-color);
  color: #fff;
}

/* Sidebar Footer */
.sidebar-footer {
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: center;
  width: 100%;
}

/* Start Chat Button */
.start-chat-btn {
  background: var(--accent-color) !important;
  color: #fff !important;
  padding: 12px 14px !important;
  border-radius: 6px !important;
  font-weight: bold !important;
  width: 100%;
  cursor: pointer !important;
  transition: background 0.2s ease;
}

.start-chat-btn:hover {
  background: #b6181a !important;
}

/* Logout Button */
.logout-btn {
  background: transparent !important;
  color: var(--accent-color) !important;
  font-weight: bold !important;
  cursor: pointer !important;
  width: 100%;
  border: 1px solid rgba(0, 0, 0, 0.3) !important;
  padding: 12px 14px !important;
  border-radius: 6px !important;
  transition: background 0.2s ease, color 0.2s ease;
  text-align: center;
  margin-bottom: 40px;
}

.logout-btn:hover {
  background: rgba(0, 0, 0, 0.1) !important;
}

/* =========================
   MAIN CONTENT (RIGHT SIDE)
========================= */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  background-color: var(--main-bg);
  padding: 50px 20px 20px;
}

/* =========================
   FIXED HEADER (Apps + Search + Button)
========================= */
.top-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60px;
  padding: 12px 24px;
  background: var(--header-bg); /* Virgin Atlantic red */
  color: #fff;               /* White text */
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.15);
  position: fixed;
  top: 0;
  left: var(--sidebar-width);
  width: calc(100% - var(--sidebar-width));
  z-index: 1000;
}
.header-title {
  font-size: 1.3rem;
  font-weight: 600;
  margin: 0;
}
/* Ensures "Apps" Title & Search Box Stay Together */
.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-grow: 1;
  margin-left: 20px;
}

.header-left h1 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  white-space: nowrap;
}

/* Fix Search Box Position */
.header-search {
  display: flex;
  align-items: center;
  background: var(--search-bg);
  border-radius: 6px;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  width: 240px;
  transition: all 0.2s ease-in-out;
}

.header-search:hover {
  border-color: var(--accent-color);
}

.search-input {
  background: transparent;
  border: none;
  color: #374151;
  padding: 6px;
  outline: none;
  flex-grow: 1;
  font-size: 0.95rem;
}

.search-icon {
  color: #6b7280;
  margin-right: 8px;
}

/* Fix New App Button Placement */
.header-controls {
  margin-left: auto;
}

.sidebar-heading {
  font-size: 14px;
  font-weight: bold;
  text-transform: uppercase;
  padding: 12px 0;
  color: rgba(255, 255, 255, 0.8);
}

.new-app-btn {
  display: flex;
  align-items: center;
  background: #ffffff;              /* White background */
  color: var(--accent-color);        /* Red text using accent variable (#d71921) */
  padding: 10px 16px;
  border-radius: 8px;
  font-weight: 600;
  border: 1px solid var(--accent-color);
  cursor: pointer;
  transition: background 0.2s ease-in-out, box-shadow 0.2s ease-in-out, color 0.2s ease;
  font-size: 0.95rem;
  white-space: nowrap;
  margin-right: 40px;
}

.new-app-btn:hover {
  background: var(--accent-color);   /* Red background on hover */
  color: #ffffff;                    /* White text on hover */
}


.new-app-btn .icon {
  margin-right: 8px;
}

/* =========================
   CHAT PAGE STYLES
========================= */
.chat-container {
  display: flex;
  height: 100vh;
  background: #f3f3f3;
  overflow: auto !important;
}

/* Chat Messages Container */
.chat-messages {
  flex-grow: 1;
  overflow-y: auto;
  padding: 80px 20px 20px;
  display: flex;
  flex-direction: column;
}

/* Chat Bubbles */
.chat-message {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
  max-width: 80%;
}

.chat-bubble {
  padding: 12px;
  border-radius: 12px;
  background: #e1e1e1;
  color: black;
}

/* User Messages */
.user-message {
  flex-direction: row-reverse;
  align-self: flex-end;
}

.user-bubble {
  background: #0078d4;
  color: white;
}

/* AI Messages */
.ai-message {
  align-self: flex-start;
}

.ai-bubble {
  background: #f3f3f3;
  color: black;
}

/* Chat Input - Fixed at Bottom */
.chat-input {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 15px;
  background: white;
  border-top: 1px solid #ddd;
  position: fixed;
  bottom: 0;
  left: var(--sidebar-width);
  width: calc(100% - var(--sidebar-width));
}

/* Input Box & Send Button */
.message-box {
  flex: 1;
  padding: 10px;
  border-radius: 5px;
  background: white;
  border: 1px solid #ddd;
}

.send-btn {
  background: #0078d4;
  color: white;
  padding: 10px 15px;
  border-radius: 5px;
  border: none;
  cursor: pointer;
}

.send-btn:hover {
  background: #005a9e;
}

.app-section-card {
  background: white;
  box-shadow: 0px 2px 10px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 30px;
  border: 1px solid #e5e7eb;
}

.app-info h4 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 700;
}

.app-info p {
  font-size: 0.9rem;
  color: #6b7280;
}

/* ✅ Sets consistent gap between cards */
.app-section-card .rs-row {
  display: contents;
  justify-content: center;
  flex-wrap: wrap;
  gap: 16px;
  /* ✅ Adds proper spacing between cards */
}

/* ✅ App Card Hover Effect */
.app-card:hover {
  transform: translateY(-3px);
}

.muted-text {
  font-size: 0.85rem;
  color: #6b7280;
  /* text-align: center; */
}

.tab-container {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  border-bottom: 2px solid #374151;
  /* Line below tabs */
  padding-bottom: 10px;
}

.tab-item {
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  font-size: 1rem;
  transition: background 0.3s ease-in-out;
  color: #9ca3af;
  /* Light text */
}

.tab-item:hover {
  background: #1e293b;
  color: white;
}

.tab-item.active {
  background: #2563eb;
  /* Blue highlight for active tab */
  color: white;
}

/* ✅ Login Page Centered Layout */
.login-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background-color: white;
}

/* ✅ Centered Login Box */
.login-content {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

/* ✅ Virgin Logo */
.logo {
  width: 180px;
  /* Slightly larger */
  margin-bottom: 20px;
}

/* ✅ Loading Animation */
.loading-gif {
  width: 100px;
  margin-bottom: 15px;
}

/* ✅ Loading Message */
.loading-message {
  font-size: 1.2rem;
  font-weight: 500;
  color: #444;
}

/* ✅ Ensure All Cards Have the Same Height */
.app-card.fixed-height {
  height: 250px;
  /* Adjust based on UI */
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* ========================= */
/* ✅ Scoped Styles for NewForm (Full Width & Height) */
/* ========================= */
.new-form-container {
  background: #f9fafb;
  /* ✅ Light Grey Background */
  border-radius: 12px;
  padding: 30px;
  margin: 20px auto;
  width: 90vw;
  /* ✅ Expands width */
  height: 80vh;
  /* ✅ Expands height */
  max-width: 1200px;
  /* ✅ Prevents excessive width */
  display: flex;
  flex-direction: column;
  box-shadow: 0px 1px 5px rgba(0, 0, 0, 0.08);
}

/* ✅ Tabs Styling */
.new-form-container .custom-tabs .rs-tabs-nav {
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 15px;
}

.new-form-container .custom-tabs .rs-tabs-tab {
  font-size: 16px;
  font-weight: 500;
  padding: 14px 20px;
  color: #374151;  /* Inactive tab text color */
  background: #ffffff; 
}

.new-form-container .custom-tabs .rs-tabs-tab-active {
  color: #d71921; /* Virgin Atlantic red for active tab text */
  border-bottom: 2px solid #d71921; /* Underline active tab with red */
  font-weight: 600;
  background: #ffffff; /* Optionally set background */
}


/* ✅ Full Height Form Panels */
.new-form-container .form-panel {
  background: white;
  padding: 24px;
  border-radius: 10px;
  border: 1px solid #e5e7eb;
  flex: 1;
  /* ✅ Makes it expand to full height */
  overflow-y: auto;
}

/* ✅ Tab Heading */
.new-form-container .tab-heading {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 20px;
}

/* ✅ Full Width Inputs */
.new-form-container .form-input {
  font-size: 14px;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #d1d5db;
  width: 100%;
  background: white;
}

/* ✅ Save Button */
.new-form-container .new-app-btn {
  background: #2563eb;
  color: white;
  padding: 12px 18px;
  border-radius: 6px;
  font-weight: bold;
  border: none;
  cursor: pointer;
  transition: background 0.2s ease;
}

.new-form-container .new-app-btn:hover {
  background: #1e40af;
}

/* ✅ Ensure Avatar and Name appear properly */
.assistant-header {
  display: flex;
  align-items: center;
  /* Align items vertically */
  gap: 12px;
  /* Add spacing between Avatar and Text */
  padding: 10px;
  /* Ensure proper padding */
  min-height: 60px;
  /* Prevent Avatar from being cut */
  overflow: hidden;
  /* Ensure it does not get clipped */
}

/* ✅ Ensure Avatar is properly sized */
.assistant-header .rs-avatar {
  width: 50px;
  /* Set consistent width */
  height: 50px;
  /* Set consistent height */
  flex-shrink: 0;
  /* Prevent shrinking */
}

/* ✅ Ensure Text is aligned properly */
.assistant-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-grow: 1;
  /* Allows text to expand without cutting */
  min-width: 0;
  /* Prevents overflow issues */
  width: 85%;
}

/* ✅ Ensure the footer has proper alignment */
.assistant-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  /* Ensures "Choose" button goes to the right */
  padding: 10px;
  /* Ensure spacing */
  width: 100%;
  /* Prevents overflow */
}

/* ✅ Ensure the "Choose" button is positioned properly */
.assistant-footer .rs-btn {
  margin-left: auto;
  /* Push the button to the right */
}

/* ✅ Section Styling */
.latest-section {
  margin-top: 30px;
  padding: 20px;
  background: #F8F8FF;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #2f3e46;
  margin-bottom: 15px;
}

/* ✅ Cards Container */
.card-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  justify-content: flex-start;
}

/* ✅ Assistant Cards */
.assistant-card {
  width: 270px;
  padding: 14px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease-in-out;
  cursor: pointer;
  border-color: #ddd;
}

.assistant-card:hover,
.news-card:hover {
  transform: translateY(-3px);
  background-color: rgba(215, 25, 33, 0.05); /* Very light red tint */
}
/* ✅ News Cards */
.news-card {
  width: 270px;
  padding: 14px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease-in-out;
  cursor: pointer;
  border-color: #ddd;
}

.news-card:hover {
  transform: translateY(-3px);
  background-color: var(--rs-btn-subtle-hover-bg);
}

/* ✅ Card Header - Avatar & Info */
.card-header,
.news-header {
  display: flex;
  align-items: center;
  gap: 10px;
  padding-bottom: 8px;
  /* border-bottom: 1px solid #e5e7eb; */
  align-items: start;
}

.assistant-avatar,
.news-avatar {
  width: 40px;
  height: 40px;
  object-fit: cover;
}

/* ✅ Title & Message */
.assistant-title,
.news-title {
  font-size: 15px;
  font-weight: 600;
  margin: 0;
  color: #2f3e46;
}

.assistant-message,
.news-snippet {
  font-size: 13px;
  color: #64748b;
  margin: 4px 0;
}

/* ✅ Footer - Type & Button */
.card-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 8px;
}

.assistant-type {
  font-size: 14px;
  color: #374151;
}

/* ✅ Choose Button */
.choose-btn {
  background: #d71921; /* Virgin Atlantic red */
  color: white;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition: background 0.2s ease-in-out;
}

.choose-btn:hover {
  background: #b6181a; /* Slightly darker red for hover */
}


/* ✅ Responsive Fixes */
@media (max-width: 768px) {
  .card-container {
    flex-direction: column;
    align-items: center;
  }
}

@media (max-width: 768px) {
  .app-section-card {
    padding: 15px;
  }

  .top-header {
    padding: 12px 16px;
  }

  .header-search {
    width: 180px;
  }

  .new-app-btn {
    padding: 8px 14px;
  }
}

.news-info {
  width: 85%;
}

.scrollable-container {
  height: 300px; /* Adjust as needed */
  overflow-y: auto;
}

.chat-history {
  margin-left: 10px;
  margin-top: 5px;
}

.chat-history-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px;
  cursor: pointer;
  border-radius: 4px;
  transition: background 0.3s ease;
}

.chat-history-item:hover {
  background: var(--hover-bg);
}

.chat-history-left {
  display: flex;
  align-items: center;
  flex: 1;
  overflow: hidden;
}

.history-icon {
  margin-right: 8px;
}
.chat-preview {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 0.9rem;
}
     